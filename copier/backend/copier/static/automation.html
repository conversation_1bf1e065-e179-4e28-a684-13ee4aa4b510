<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Automations</title>
    <script defer src="/static/js/alpine_3.x.x.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link rel="stylesheet" href="/static/css/automation_styles.css">
</head>
<body>
    <div x-data="automationApp()" class="container">
        <!-- Version Info -->
        <div x-show="serverVersion" class="version-info" style="position: fixed; bottom: 10px; left: 10px; z-index: 1000; background: rgba(0,0,0,0.7); color: white; padding: 4px 8px; border-radius: 4px; font-size: 11px;">
            <span x-text="'Server: ' + serverVersion"></span>
        </div>

        <div class="main-header">
            <div class="header-left">
                <h1>Automations</h1>
                <div class="last-updated" x-show="lastUpdated">
                    Last Updated: <span x-text="formatDate(lastUpdated)"></span>
                </div>
                <button @click="togglePause()" class="pause-btn" :title="isPaused ? 'Resume auto-refresh' : 'Pause auto-refresh'">
                    <i class="fas" :class="isPaused ? 'fa-play' : 'fa-pause'"></i>
                </button>
            </div>
            <div class="global-actions">
                <div class="new-schedule-container">
                    <select x-model="selectedJobType" class="job-type-selector">
                        <option value="">Default</option>
                        <option value="upgrade_extension">Upgrade Extension</option>
                        <option value="update_stats">Update Stats</option>
                    </select>
                    <button @click="newSchedule()" :disabled="isLoading('new_schedule')" title="Create and start a new automation schedule for today.">
                        <i class="fas fa-play"></i>
                        <span x-show="!isLoading('new_schedule')">New Schedule</span>
                        <span x-show="isLoading('new_schedule')" class="loading-spinner-small"></span>
                    </button>
                </div>
                <button @click="cleanupSchedules()" class="secondary-btn" :disabled="isLoading('cleanup_schedules')" title="Delete all completed or canceled schedules.">
                    <i class="fas fa-broom"></i>
                    <span x-show="!isLoading('cleanup_schedules')">Cleanup Schedules</span>
                    <span x-show="isLoading('cleanup_schedules')" class="loading-spinner-small"></span>
                </button>
            </div>
        </div>
        <div class="main-layout">
            <!-- Loading State -->
            <div x-show="loading" class="loading-container">
                <div class="loading-spinner"></div>
                <p>Loading schedules...</p>
            </div>

            <!-- Error State -->
            <div x-show="!loading && error" class="error-container">
                <h2>Error</h2>
                <p x-text="error"></p>
                <button @click="loadSchedules()">Retry</button>
            </div>

            <!-- Empty State -->
            <div x-show="!loading && !error && schedules.length === 0" class="empty-state">
                <h2>No Schedules Found</h2>
                <p>There are no automation schedules to display.</p>
            </div>

            <!-- Main Content -->
            <template x-if="!loading && !error && schedules.length > 0">
                <div class="main-content-wrapper">
                    <!-- Sidebar -->
                    <div class="sidebar">
                        <div class="sidebar-item" :class="{ 'active': currentView === 'schedules' }" @click="showSchedules()">Schedules</div>
                        <div class="schedule-list">
                            <template x-for="(schedulesOnDate, date) in groupedSchedules" :key="date">
                                <div class="date-group">
                                    <div class="date-header">
                                        <h4 x-text="date"></h4>
                                    </div>
                                    <div x-show="true">
                                        <template x-for="schedule in schedulesOnDate" :key="schedule.refID">
                                            <div class="sidebar-schedule-item" :class="{ 'selected': currentSchedule && currentSchedule.refID === schedule.refID, 'active-schedule': isActiveSchedule(schedule) }">
                                                <div class="schedule-header" @click="showScheduleDetail(schedule.refID)">
                                                    <div class="sidebar-schedule-info">
                                                        <div class="schedule-label">
                                                            <span x-text="schedule.refID.slice(-6)"></span>
                                                            <span x-html="getScheduleStatusBadge(schedule)"></span>
                                                        </div>
                                                        <div class="schedule-meta">
                                                            <span class="job-abbreviation" x-text="getJobAbbreviation(schedule.job)" :title="getJobDisplayName(schedule.job)"></span>
                                                            <span x-text="formatDate(schedule.createTime)"></span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </template>
                                    </div>
                                </div>
                            </template>
                        </div>
                    </div>

                    <!-- Main Content -->
                    <div class="content">
                        <!-- No Schedule Selected State -->
                        <div x-show="!currentSchedule" class="no-schedule-selected">
                            <i class="fas fa-arrow-left"></i>
                            <p>Select a schedule from the left to see its details.</p>
                        </div>

                        <!-- Schedule Detail Page -->
                        <div x-show="currentSchedule" class="schedule-detail">
                            <h2>Schedule Detail</h2>
                            <template x-if="currentSchedule">
                                <div>
                                    <div class="schedule-actions">
                                        <button x-show="!currentSchedule.pauseTime && !currentSchedule.endTime && !currentSchedule.cancelTime" @click="pauseSchedule()" class="action-btn" :disabled="isLoading('pause_schedule')" title="Pause the currently running schedule.">
                                            <i class="fas fa-pause"></i>
                                            <span x-show="!isLoading('pause_schedule')">Pause</span>
                                            <span x-show="isLoading('pause_schedule')" class="loading-spinner-small"></span>
                                        </button>
                                        <button x-show="currentSchedule.pauseTime && !currentSchedule.cancelTime" @click="resumeSchedule()" class="action-btn" :disabled="isLoading('resume_schedule')" title="Resume the paused schedule.">
                                            <i class="fas fa-play"></i>
                                            <span x-show="!isLoading('resume_schedule')">Resume</span>
                                            <span x-show="isLoading('resume_schedule')" class="loading-spinner-small"></span>
                                        </button>
                                        <button x-show="!currentSchedule.endTime && !currentSchedule.cancelTime" @click="cancelSchedule()" class="action-btn danger" :disabled="isLoading('cancel_schedule')" title="Cancel the schedule. This cannot be undone.">
                                            <i class="fas fa-stop-circle"></i>
                                            <span x-show="!isLoading('cancel_schedule')">Cancel</span>
                                            <span x-show="isLoading('cancel_schedule')" class="loading-spinner-small"></span>
                                        </button>
                                    </div>

                                    <div class="schedule-info">
                                        <div class="info-row">
                                            <span>RefID:</span>
                                            <div>
                                                <span x-text="currentSchedule.refID"></span>
                                                <i class="fas fa-copy copy-icon" @click="copyToClipboard(currentSchedule.refID, 'Schedule RefID')"></i>
                                            </div>
                                        </div>
                                        <div class="info-row">
                                            <span>Date:</span>
                                            <span x-text="currentSchedule.date"></span>
                                        </div>
                                        <div class="info-row">
                                            <span>Job:</span>
                                            <span x-text="getJobDisplayName(currentSchedule.job)"></span>
                                        </div>
                                        <div class="info-row">
                                            <span>Daily Target:</span>
                                            <span x-text="currentSchedule.dailyVolumeLimit ? currentSchedule.dailyVolumeLimit.toFixed(0) : 'N/A'"></span>
                                        </div>
                                        <div class="info-row">
                                            <span>Status:</span>
                                            <span x-html="getScheduleStatusBadge(currentSchedule)"></span>
                                        </div>
                                        <div class="info-row">
                                            <span>Summary:</span>
                                            <span x-text="`${currentSchedule.batches.length} batches, ${currentSchedule.batches.reduce((acc, batch) => acc + batch.items.length, 0)} items`"></span>
                                        </div>
                                        <div class="info-row">
                                            <span>Created:</span>
                                            <span x-text="formatDate(currentSchedule.createTime)"></span>
                                        </div>
                                        <div class="info-row" x-show="currentSchedule.startTime">
                                            <span>Started:</span>
                                            <span x-text="formatDate(currentSchedule.startTime)"></span>
                                        </div>
                                        <div class="info-row" x-show="currentSchedule.pauseTime">
                                            <span>Paused:</span>
                                            <span x-text="formatDate(currentSchedule.pauseTime)"></span>
                                        </div>
                                        <div class="info-row" x-show="currentSchedule.endTime">
                                            <span>Ended:</span>
                                            <span x-text="formatDate(currentSchedule.endTime)"></span>
                                        </div>
                                        <div class="info-row" x-show="currentSchedule.cancelTime">
                                            <span>Canceled:</span>
                                            <span x-text="formatDate(currentSchedule.cancelTime)"></span>
                                        </div>
                                    </div>

                                    <div class="no-batches-message" x-show="!currentSchedule.batches || currentSchedule.batches.length === 0">
                                        <p>This schedule has no batches.</p>
                                    </div>

                                    <div class="schedule-summary" x-show="currentSchedule.job === ''">
                                        <strong>Schedule Summary:</strong>
                                        <div class="summary-values">
                                            <span x-text="`Total Volume: ${getScheduleSummary().totalVolume}`"></span>
                                            <span class="separator">|</span>
                                            <span x-text="`Total Cost: $${getScheduleSummary().totalCost}`"></span>
                                            <span class="separator">|</span>
                                            <span x-text="`Cost Ratio: ${getScheduleSummary().costRatio}%`"></span>
                                        </div>
                                    </div>

                                    <div class="batches">
                                        <template x-for="batch in currentSchedule.batches" :key="batch.refID">
                                            <div class="batch-item" :id="`batch-${batch.refID}`" :class="{ 'current-batch': currentSchedule.currentBatch && batch.refID === currentSchedule.currentBatch.refID }">
                                                <div class="batch-header">
                                                    <h3>
                                                        Batch: <span x-text="batch.refID"></span>
                                                        <i class="fas fa-copy copy-icon" @click="copyToClipboard(batch.refID, 'Batch RefID')"></i>
                                                    </h3>
                                                    <div class="batch-meta">
                                                        <span x-show="batch.startTime" x-text="`Started: ${formatTime(batch.startTime)}`"></span>
                                                        <span x-show="batch.endTime" x-text="`Ended: ${formatTime(batch.endTime)}`"></span>
                                                        <span x-show="batch.runningTime > 0" x-text="`Running: ${formatDuration(batch.runningTime)}`" class="batch-running-time"></span>
                                                        <span x-text="`Wait: ${batch.waitMinutes}m`"></span>
                                                        <div x-data="{ open: false }" class="relative inline-block">
                                                            <button @click="open = !open" class="batch-action-btn" :class="{'loading': batchLoading[batch.refID]}">
                                                                <template x-if="!batchLoading[batch.refID]">
                                                                    <i class="fas fa-ellipsis-h"></i>
                                                                </template>
                                                                <template x-if="batchLoading[batch.refID]">
                                                                    <span class="loading-spinner-small"></span>
                                                                </template>
                                                            </button>
                                                            <div x-show="open" @click.away="open = false" class="absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-10">
                                                                <div class="py-1" role="menu" aria-orientation="vertical" aria-labelledby="options-menu">
                                                                    <a href="#" @click.prevent="openBatchChromes(batch, $event.target); open = false" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" role="menuitem"><i class="fas fa-folder-open"></i> Open Chromes</a>
                                                                    <a href="#" @click.prevent="closeBatchChromes(batch, $event.target); open = false" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" role="menuitem"><i class="fas fa-times-circle"></i> Close Chromes</a>
                                                                    <a href="#" @click.prevent="refreshBatchChromes(batch, $event.target); open = false" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" role="menuitem"><i class="fas fa-sync-alt"></i> Refresh</a>
                                                                    <a href="#" @click.prevent="navigateToTrade(batch, $event.target); open = false" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" role="menuitem"><i class="fas fa-location-arrow"></i> Goto Trade</a>
                                                                    <div class="border-t border-gray-200 my-1"></div>
                                                                    <a href="#" @click.prevent="goToOverviewForBatch(batch); open = false" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" role="menuitem"><i class="fas fa-list-alt"></i> Overview</a>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="progress-bar-container" x-show="currentSchedule.job === ''">
                                                    <div class="progress-bar">
                                                        <div class="progress-segment volume" :style="`width: ${getBatchProgress(batch).volume}%`"></div>
                                                    </div>
                                                    <div class="progress-label" x-text="getVolumeProgress(batch)"></div>
                                                </div>
                                                <div class="progress-bar-container" x-show="currentSchedule.job !== ''">
                                                    <div class="progress-bar">
                                                        <div class="progress-segment status" :style="`width: ${getBatchProgress(batch).status}%`"></div>
                                                    </div>
                                                    <div class="progress-label" x-text="getStatusProgress(batch)"></div>
                                                </div>
                                                <div class="batch-errors" x-show="batch.errorEvents && batch.errorEvents.length > 0" x-data="{ errorsVisible: expandedErrors.has(batch.refID) }">
                                                    <div class="batch-errors-header" @click="toggleErrorVisibility(batch.refID)">
                                                        <h4>Errors (<span x-text="batch.errorEvents.length"></span>)</h4>
                                                        <button class="toggle-errors-btn">
                                                            <i class="fas" :class="expandedErrors.has(batch.refID) ? 'fa-chevron-up' : 'fa-chevron-down'"></i>
                                                        </button>
                                                    </div>
                                                    <div x-show="expandedErrors.has(batch.refID)" class="error-events-list">
                                                        <div class="error-event-header">
                                                            <span class="error-time">Time</span>
                                                            <span class="error-project">Project</span>
                                                            <span class="error-user">User</span>
                                                            <span class="error-type">Type</span>
                                                            <span class="error-comment">Comment</span>
                                                        </div>
                                                        <template x-for="(error, index) in batch.errorEvents" :key="index">
                                                            <div class="error-event">
                                                                <span class="error-time" x-text="formatDate(error.time)"></span>
                                                                <span class="error-project" x-text="error.projectID"></span>
                                                                <span class="error-user">
                                                                    <span x-text="getAgentAliasByUserID(error.projectID, error.userID)"></span>
                                                                    <span x-text="shortenAddress(error.userID)" @click="copyToClipboard(error.userID, 'User ID')" :title="error.userID"></span>
                                                                </span>
                                                                <span class="error-type" x-text="error.type"></span>
                                                                <span class="error-comment" x-text="error.comment"></span>
                                                            </div>
                                                        </template>
                                                    </div>
                                                </div>
                                                <div class="automation-items-list">
                                                    <template x-for="item in batch.items" :key="item.refID">
                                                        <div class="automation-item-row" :class="item.status">
                                                            <div class="action-buttons" x-show="currentSchedule.job === ''">
                                                                <button class="settings-button" @click="openSettings(item.projectID, 'master')">
                                                                    <svg class="settings-icon" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                                        <circle cx="12" cy="12" r="3"></circle>
                                                                        <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"></path>
                                                                    </svg>
                                                                </button>
                                                                <button class="status-toggle-btn" 
                                                                    :class="getProjectStatus(item.projectID).toLowerCase()"
                                                                    @click="toggleProjectStatus(item.projectID, getProjectStatus(item.projectID))"
                                                                    :title="getProjectStatus(item.projectID).toLowerCase() === 'running' ? 'Pause' : 'Resume'">
                                                                    <template x-if="getProjectStatus(item.projectID).toLowerCase() === 'running'">
                                                                        <svg class="status-icon" width="24" height="24" viewBox="0 0 16 16" fill="currentColor">
                                                                            <rect x="4" y="2" width="3" height="12"></rect>
                                                                            <rect x="9" y="2" width="3" height="12"></rect>
                                                                        </svg>
                                                                    </template>
                                                                    <template x-if="getProjectStatus(item.projectID).toLowerCase() !== 'running'">
                                                                        <svg class="status-icon" width="24" height="24" viewBox="0 0 16 16" fill="currentColor">
                                                                            <path d="M4 2l8 6-8 6z"></path>
                                                                        </svg>
                                                                    </template>
                                                                </button>
                                                            </div>
                                                            <strong class="item-project-id" @click="goToProject(item.projectID)" x-text="item.projectID"></strong>
                                                            <div class="status-container">
                                                                <span class="status-badge" :class="getProjectStatusClass(item.projectID)" x-text="getProjectStatus(item.projectID)" :title="getProjectStatusTooltip(item.projectID)"></span>
                                                            </div>
                                                            <div class="agent-update-time" x-show="currentSchedule.job !== 'update_stats'">
                                                                <span class="agent-alias" x-text="getAgentAlias(item.projectID, 'master')"></span>
                                                                <span class="timestamp" @click="activateAgentChrome(item.projectID, 'master')" :title="getAgentUpdateTimeTooltip(item.projectID, 'master')" x-text="getAgentUpdateTime(item.projectID, 'master')" :class="{ 'timestamp-stale': getAgentUpdateTimeInfo(item.projectID, 'master').isStale }" :disabled="isLoading('activate_chrome_' + item.projectID + '_master')"></span>
                                                            </div>
                                                            <div class="agent-update-time" x-show="currentSchedule.job === 'update_stats'">
                                                                <span class="agent-alias" x-text="getAgentAlias(item.projectID, 'master')"></span>
                                                                <span class="timestamp" @click="activateAndRequestStats(item.projectID, 'master')" :title="getAgentStatsTime(item.projectID, 'master', true)" x-text="getAgentStatsTime(item.projectID, 'master', false)" :class="{ 'timestamp-stale': getAgentStatsTimeInfo(item.projectID, 'master').isStale }" :disabled="isLoading('activate_and_request_stats_' + item.projectID + '_master')"></span>
                                                            </div>
                                                            <div class="agent-update-time" x-show="currentSchedule.job !== 'update_stats'">
                                                                <span class="agent-alias" x-text="getAgentAlias(item.projectID, 'copier')"></span>
                                                                <span class="timestamp" @click="activateAgentChrome(item.projectID, 'copier')" :title="getAgentUpdateTimeTooltip(item.projectID, 'copier')" x-text="getAgentUpdateTime(item.projectID, 'copier')" :class="{ 'timestamp-stale': getAgentUpdateTimeInfo(item.projectID, 'copier').isStale }" :disabled="isLoading('activate_chrome_' + item.projectID + '_copier')"></span>
                                                            </div>
                                                            <div class="agent-update-time" x-show="currentSchedule.job === 'update_stats'">
                                                                <span class="agent-alias" x-text="getAgentAlias(item.projectID, 'copier')"></span>
                                                                <span class="timestamp" @click="activateAndRequestStats(item.projectID, 'copier')" :title="getAgentStatsTime(item.projectID, 'copier', true)" x-text="getAgentStatsTime(item.projectID, 'copier', false)" :class="{ 'timestamp-stale': getAgentStatsTimeInfo(item.projectID, 'copier').isStale }" :disabled="isLoading('activate_and_request_stats_' + item.projectID + '_copier')"></span>
                                                            </div>
                                                            <div class="daily-volume" x-show="currentSchedule.job === ''">
                                                                <span x-text="item.dailyPerformance ? item.dailyPerformance.volume.toFixed(0) : '0'"></span>
                                                            </div>
                                                            <div class="trades-action" x-show="currentSchedule.job === ''">
                                                                <button @click="$dispatch('show-position-changes', { projectId: item.projectID, showMasterAndCopier: true })" class="trades-btn" x-text="getProjectTradeCount(item.projectID) + ' trades'"></button>
                                                            </div>
                                                            <div style="flex-grow: 1;"></div>
                                                            <span class="item-time" x-text="formatTime(item.updateTime)"></span>
                                                            <div class="status-container">
                                                                <span class="status-badge" :class="item.status" x-text="item.status" :title="item.status"></span>
                                                            </div>
                                                        </div>
                                                    </template>
                                                </div>
                                                <div class="batch-summary-row" x-show="currentSchedule.job === ''">
                                                    <strong>Summary</strong>
                                                    <div class="summary-values">
                                                        <span x-text="`Volume: ${getBatchSummary(batch).totalVolume}`"></span>
                                                        <span x-text="`Cost: $${getBatchSummary(batch).totalCost}`"></span>
                                                        <span x-text="`Ratio: ${getBatchSummary(batch).costRatio}%`"></span>
                                                    </div>
                                                </div>
                                            </div>
                                        </template>
                                    </div>
                                    <div class="schedule-summary" x-show="currentSchedule.job === ''">
                                        <strong>Schedule Summary:</strong>
                                        <div class="summary-values">
                                            <span x-text="`Total Volume: ${getScheduleSummary().totalVolume}`"></span>
                                            <span class="separator">|</span>
                                            <span x-text="`Total Cost: $${getScheduleSummary().totalCost}`"></span>
                                            <span class="separator">|</span>
                                            <span x-text="`Cost Ratio: ${getScheduleSummary().costRatio}%`"></span>
                                        </div>
                                    </div>
                                </div>
                            </template>
                        </div>
                    </div>
                </div>
            </template>
        </div>
        <!-- Position Changes Dialog -->
        <div 
            x-data="positionChangesDialog"
            x-ref="positionChangesDialog"
            x-show="showPositionChanges" 
            class="dialog-overlay"
            @click.away="showPositionChanges = false"
            @show-position-changes.window="fetchPositionChanges($event.detail.projectId, $event.detail.userId, $event.detail.showMasterAndCopier)"
            x-cloak
        >
            <div class="dialog-content" style="max-width: 1200px;">
                <div class="dialog-header">
                    <h3 x-text="!showMasterAndCopier ? 'Trades - ' + (singleAgentInfo?.alias ? '[' + singleAgentInfo.alias + ']' : '') : 'Trades - ' + selectedProject"></h3>
                    <button @click="showPositionChanges = false" class="close-button">&times;</button>
                </div>
                
                <div x-show="positionChangesLoading" class="loading-container">
                    <div class="loading-spinner"></div>
                    <p>Loading position changes...</p>
                </div>

                <div x-show="!positionChangesLoading && positionChangesError" class="error-container">
                    <div class="error-icon">⚠️</div>
                    <p x-text="positionChangesError"></p>
                    <button @click="fetchPositionChanges(selectedProject, selectedAgent, showMasterAndCopier)" class="retry-button">Retry</button>
                </div>

                <div x-show="!positionChangesLoading && !positionChangesError" class="position-changes-list">
                    <template x-if="!showMasterAndCopier && (!positionChanges || positionChanges.length === 0)">
                        <div class="empty-state">
                            <p>No position changes found.</p>
                        </div>
                    </template>
                    <div class="position-changes-container" x-bind:class="{ 'two-column': showMasterAndCopier }">
                        <template x-if="!showMasterAndCopier">
                            <template x-if="positionChanges && positionChanges.length > 0">
                                <template x-for="(change, index) in positionChanges" :key="index">
                                    <div x-html="formatPositionChange(change)"></div>
                                </template>
                            </template>
                        </template>
                        <template x-if="showMasterAndCopier">
                            <div class="position-changes-grid">
                                <div class="position-changes-column">
                                    <h4 x-text="'Master - ' + (masterAgentInfo?.alias ? '[' + masterAgentInfo.alias + '] ' : '') + masterAgentInfo?.address"></h4>
                                    <template x-if="masterPositionChanges && masterPositionChanges.length > 0">
                                        <template x-for="(change, index) in masterPositionChanges" :key="index">
                                            <div x-html="formatPositionChange(change)"></div>
                                        </template>
                                    </template>
                                    <template x-if="!masterPositionChanges || masterPositionChanges.length === 0">
                                        <div class="empty-state">
                                            <p>No position changes for master agent.</p>
                                        </div>
                                    </template>
                                </div>
                                <div class="position-changes-column">
                                    <h4 x-text="copierAgentInfo ? 'Copier - ' + (copierAgentInfo.alias ? '[' + copierAgentInfo.alias + '] ' : '') + copierAgentInfo.address : 'No Copier Agent'"></h4>
                                    <template x-if="copierPositionChanges && copierPositionChanges.length > 0">
                                        <template x-for="(change, index) in copierPositionChanges" :key="index">
                                            <div x-html="formatPositionChange(change)"></div>
                                        </template>
                                    </template>
                                    <template x-if="!copierPositionChanges || copierPositionChanges.length === 0">
                                        <div class="empty-state">
                                            <p>No position changes for copier agent.</p>
                                        </div>
                                    </template>
                                </div>
                            </div>
                        </template>
                    </div>
                </div>
            </div>
        </div>
        <!-- Settings Dialog -->
        <div 
            x-show="showSettings" 
            class="dialog-overlay"
            @click.away="showSettings = false"
            x-cloak
        >
            <div class="dialog-content" style="max-width: 600px;">
                <div class="dialog-header">
                    <h3>
                        <template x-if="currentSettings.alias">
                            <span x-text="'[' + currentSettings.alias + '] '" class="agent-alias"></span>
                        </template>
                        <span x-text="selectedAgentForSettings ? selectedAgentForSettings.slice(0, 6) + '...' + selectedAgentForSettings.slice(-4) : ''"></span>
                        <span class="agent-role" :data-role="selectedAgentRoleForSettings.toLowerCase()" x-text="selectedAgentRoleForSettings"></span>
                    </h3>
                    <button @click="showSettings = false" class="close-button">&times;</button>
                </div>
                
                <div x-show="settingsLoading" class="loading-container">
                    <div class="loading-spinner"></div>
                    <p>Loading settings...</p>
                </div>

                <div x-show="!settingsLoading && settingsError" class="error-container">
                    <div class="error-icon">⚠️</div>
                    <p x-text="settingsError"></p>
                    <button @click="fetchSettings(selectedAgentForSettings)" class="retry-button">Retry</button>
                </div>

                <div x-show="!settingsLoading && !settingsError" class="settings-form">
                    <form @submit.prevent="saveSettings">
                        <div class="form-group">
                            <label for="alias">Alias</label>
                            <input type="text" id="alias" x-model="currentSettings.alias" class="form-control">
                        </div>

                        <div class="form-group">
                            <label for="defaultSymbol">Default Symbol</label>
                            <input type="text" id="defaultSymbol" x-model="currentSettings.defaultSymbol" class="form-control">
                        </div>

                        <div class="form-group" x-show="selectedAgentRoleForSettings === 'COPIER'">
                            <label for="copyFromId">Copy From ID</label>
                            <input type="text" id="copyFromId" x-model="currentSettings.copyFromId" class="form-control">
                        </div>

                        <div class="form-group" x-show="selectedAgentRoleForSettings === 'COPIER'">
                            <label for="copyPercentage">Copy Percentage</label>
                            <input type="number" id="copyPercentage" x-model="currentSettings.copyPercentage" class="form-control" min="0" max="100">
                        </div>

                        <div class="form-group">
                            <label for="minMargin">Minimum Margin</label>
                            <input type="number" id="minMargin" x-model="currentSettings.minMargin" class="form-control" step="0.01">
                        </div>

                        <div class="form-group" x-show="selectedAgentRoleForSettings === 'MASTER'">
                            <label for="defaultSize">Default Size</label>
                            <input type="number" id="defaultSize" x-model="currentSettings.defaultSize" class="form-control" min="0" step="0.001">
                        </div>

                        <div class="form-group" x-show="selectedAgentRoleForSettings === 'MASTER'">
                            <label for="longRatio">Long Ratio</label>
                            <input type="number" id="longRatio" x-model="currentSettings.longRatio" class="form-control" min="0" max="1" step="0.01">
                            <small class="form-text text-muted">Probability of opening long positions (0-1)</small>
                        </div>

                        <div class="form-group" x-show="selectedAgentRoleForSettings === 'MASTER'">
                            <label for="coolingHour">Cooling Hour</label>
                            <input type="number" id="coolingHour" x-model="currentSettings.coolingHour" class="form-control" min="-0.5" max="48.0" step="0.001">
                            <small class="form-text text-muted">For exact hour mode: -0.5 to 0, For interval mode: 0.01 to 48.0</small>
                        </div>

                        <div class="form-group">
                            <label for="refreshInterval">Refresh Interval (minutes)</label>
                            <input type="number" id="refreshInterval" x-model="currentSettings.refreshInterval" class="form-control" min="1">
                        </div>

                        <div class="form-group" x-show="selectedAgentRoleForSettings === 'MASTER'">
                            <label for="stopLossBuffer">Stop Loss Buffer</label>
                            <input type="number" id="stopLossBuffer" x-model="currentSettings.stopLossBuffer" class="form-control" min="0" max="0.05" step="0.0001">
                            <small class="form-text text-muted">Buffer from liquidation price (0-0.05)</small>
                        </div>

                        <div class="form-group">
                            <label class="checkbox-label">
                                <input type="checkbox" x-model="currentSettings.debug">
                                <span>Debug Mode</span>
                            </label>
                        </div>

                        <div class="form-group">
                            <label class="checkbox-label">
                                <input type="checkbox" x-model="currentSettings.manualMode">
                                <span>Manual Mode</span>
                            </label>
                        </div>

                        <div class="form-actions">
                            <button type="button" @click="showSettings = false" class="cancel-button">Cancel</button>
                            <button type="submit" class="save-button">Save Changes</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <script src="/static/js/automation_app.js"></script>

</body>
</html>
