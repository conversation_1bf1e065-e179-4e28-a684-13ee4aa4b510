package pgate

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"math/rand"
	"net/http"
	"net/url"
	"os"
	"strings"
	"sync"
	"time"

	"github.com/fatih/color"
	"github.com/wizhodl/quanter/common/zlog"
	"golang.org/x/crypto/ssh"
)

type ProxyStatus string

const (
	ProxyStatusOK       ProxyStatus = "ok"
	ProxyStatusFail     ProxyStatus = "fail"
	ProxyStatusCreating ProxyStatus = "creating"
	ProxyStatusCreated  ProxyStatus = "created"
	ProxyStatusDeleting ProxyStatus = "deleting"
	ProxyStatusDeleted  ProxyStatus = "deleted"
	ProxyStatusStopping ProxyStatus = "stopping"
	ProxyStatusStopped  ProxyStatus = "stopped"
)

func (status ProxyStatus) ColoredString() (statusStr string) {
	if status == ProxyStatusOK {
		statusStr = color.GreenString(string(status))
	} else if status == ProxyStatusFail {
		statusStr = color.RedString(string(status))
	} else {
		statusStr = color.YellowString(string(status))
	}
	return
}

type InstanceState string

const (
	InstanceStatePending    InstanceState = "pending"
	InstanceStateRunning    InstanceState = "running"
	InstanceStateShutting   InstanceState = "shutting-down"
	InstanceStateTerminated InstanceState = "terminated"
	InstanceStateStopping   InstanceState = "stopping"
	InstanceStateStopped    InstanceState = "stopped"
	InstanceStateNotFound   InstanceState = "not-found"
	InstanceStateUnknown    InstanceState = "unknown"
)

type ProxyType string

const (
	ProxyTypeDefault ProxyType = ""
	ProxyTypeSocks5  ProxyType = "socks5"
)

type Proxy struct {
	Type      ProxyType   `json:"type"`
	Name      string      `json:"name"`
	Group     string      `json:"group"`
	LifeHour  int         `json:"life_hour"`
	Status    ProxyStatus `json:"status"`
	RespTime  int64       `json:"resp_time"`
	CreatedAt time.Time   `json:"created_at"`
	DeletedAt *time.Time  `json:"deleted_at"`

	// for ProxyTypeSocks5,  use: Username, Password, IP, Port, to store the proxy info
	Username string `json:"username"` // not used for ProxyTypeDefault
	Password string `json:"password"`
	IP       string `json:"ip"`
	Port     int    `json:"port"` // not used for ProxyTypeDefault

	mutex sync.Mutex

	// for ProxyTypeDefault, also use the following fields
	template     *ProxyTemplate
	TemplateName string `json:"template_name"`
	Domain       string `json:"domain"`
	InstanceID   string `json:"instance_id"`
	WARPEnabled  bool   `json:"warp_enabled"`
	// the following fields should be updated asynchronously
	InstanceIP    string        `json:"instance_ip"`
	InstanceState InstanceState `json:"instance_state"`
	DNSOK         bool          `json:"dns_ok"`
	Accessible    bool          `json:"accessible"`
}

func (p *Proxy) GetAddr() string {
	if p.Type == ProxyTypeSocks5 {
		return fmt.Sprintf("%s:%d", p.IP, p.Port)
	}
	return fmt.Sprintf("%s.%s", p.Name, p.Domain)
}

func (p *Proxy) GetDomain() string {
	return fmt.Sprintf("%s.%s", p.Name, p.Domain)
}

func (p *Proxy) IsLifeOver() bool {
	if p.LifeHour == 0 {
		return false
	}

	return time.Since(p.CreatedAt).Hours() > float64(p.LifeHour)
}

func NewProxy(name string, template *ProxyTemplate, lifeHour int, group string) *Proxy {
	domains := template.Domains
	domain := domains[rand.Intn(len(domains))] // 随机选择一个域名

	return &Proxy{
		template:     template,
		TemplateName: template.Name,
		Name:         name,
		Group:        group,
		Domain:       domain,
		Password:     randomPassword(16),
		Status:       ProxyStatusCreating,
		CreatedAt:    time.Now(),
		LifeHour:     lifeHour,
		WARPEnabled:  false,
	}
}

func NewSocks5Proxy(name string, username string, password string, ip string, port int, lifeHour int, group string) *Proxy {
	return &Proxy{
		Type:      ProxyTypeSocks5,
		Name:      name,
		Group:     group,
		Username:  username,
		Password:  password,
		IP:        ip,
		Port:      port,
		Status:    ProxyStatusCreating,
		CreatedAt: time.Now(),
		LifeHour:  lifeHour,
	}
}

func (p *Proxy) Create() error {
	p.mutex.Lock()
	defer p.mutex.Unlock()

	if p.Type == ProxyTypeSocks5 {
		p.Status = ProxyStatusOK
		return nil
	}

	err := p.create()
	if err != nil {
		return err
	}

	p.accessibleCheckLoop()

	zlog.Infof("proxy created, domain: %s, status: %s", p.GetDomain(), p.Status)

	// 需要在检测可访问后再执行 WARP 操作，确保代理已可用，否则可能导致 trojan 443 端口无法访问
	p.setWARP()

	return nil
}

func (p *Proxy) create() error {
	if p.Type != ProxyTypeDefault {
		return fmt.Errorf("only default proxy support create")
	}

	p.Status = ProxyStatusCreating

	err := p.createInstance()
	if err != nil {
		p.Status = ProxyStatusFail
		return err
	}

	zlog.Infof("proxy instance created, name: %s, instance id: %s, public ip: %s", p.Name, p.InstanceID, p.IP)

	err = p.createDNSRecord()
	if err != nil {
		p.Status = ProxyStatusFail
		return fmt.Errorf("create DNS record failed: %v", err)
	}

	zlog.Infof("proxy DNS record created")

	err = p.InstallTrojan()
	if err != nil {
		p.Status = ProxyStatusFail
		return fmt.Errorf("install Trojan failed: %v", err)
	}

	zlog.Infof("proxy Trojan installed")

	p.Status = ProxyStatusCreated
	return nil
}

func (p *Proxy) accessibleCheckLoop() {
	for i := 0; i < 5; i++ {
		p.accessibleCheck()
		if p.Accessible {
			break
		}
		time.Sleep(10 * time.Second)
	}
}

func (p *Proxy) setWARP() {
	if p.Type != ProxyTypeDefault {
		return
	}

	if p.template.EnableWARP {
		err := p.EnableWARP()
		if err != nil {
			zlog.Errorf("enable WARP failed: %v", err)
		} else {
			p.WARPEnabled = true
		}
	} else {
		err := p.DisableWARP()
		if err != nil {
			zlog.Errorf("disable WARP failed: %v", err)
		} else {
			p.WARPEnabled = false
		}
	}
}

func (p *Proxy) deleteInstance() error {
	if p.Type != ProxyTypeDefault {
		return nil
	}

	if p.template.Service == ServiceAWS {
		return p.deleteAWSInstance()
	}

	if p.template.Service == ServiceGOOGLE {
		return p.deleteGoogleInstance()
	}

	return fmt.Errorf("unsupported service: %s", p.template.Service)
}

func (p *Proxy) Delete() error {
	p.mutex.Lock()
	defer p.mutex.Unlock()

	if p.Type == ProxyTypeSocks5 {
		p.Status = ProxyStatusDeleted
		now := time.Now()
		p.DeletedAt = &now
		return nil
	}

	p.Status = ProxyStatusDeleting
	err := p.deleteInstance()
	if err != nil {
		p.Status = ProxyStatusFail
		return err
	}

	err = p.deleteDNSRecord()
	if err != nil {
		p.Status = ProxyStatusFail
		return fmt.Errorf("delete DNS record failed: %v", err)
	}

	p.Status = ProxyStatusDeleted
	now := time.Now()
	p.DeletedAt = &now

	return nil
}

func (p *Proxy) Stop() error {
	p.mutex.Lock()
	defer p.mutex.Unlock()

	if p.Type != ProxyTypeDefault {
		return fmt.Errorf("only default proxy support stop")
	}

	if p.Status != ProxyStatusOK && p.Status != ProxyStatusFail {
		return fmt.Errorf("only support stop status is OK or Fail")
	}

	p.Status = ProxyStatusStopping
	err := p.deleteInstance()
	if err != nil {
		p.Status = ProxyStatusFail
		return fmt.Errorf("delete AWS instance failed: %v", err)
	}

	err = p.deleteDNSRecord()
	if err != nil {
		p.Status = ProxyStatusFail
		return fmt.Errorf("delete DNS record failed: %v", err)
	}

	p.Accessible = false
	p.Status = ProxyStatusStopped

	return nil
}

func (p *Proxy) Start() error {
	p.mutex.Lock()
	defer p.mutex.Unlock()

	if p.Type != ProxyTypeDefault {
		return fmt.Errorf("only default proxy support start")
	}

	if p.Status != ProxyStatusStopped {
		return fmt.Errorf("only support start status is Stopped")
	}

	err := p.create()
	if err != nil {
		return fmt.Errorf("create proxy failed: %v", err)
	}

	p.accessibleCheckLoop()

	zlog.Infof("proxy started, domain: %s, status: %s", p.GetDomain(), p.Status)

	p.setWARP()

	return nil
}

func (p *Proxy) Repair() error {
	if p.Type != ProxyTypeDefault {
		return fmt.Errorf("only default proxy support repair")
	}

	err := p.Stop()
	if err != nil {
		return fmt.Errorf("stop proxy failed: %v", err)
	}

	err = p.Start()
	if err != nil {
		return fmt.Errorf("start proxy failed: %v", err)
	}

	return nil
}

func (p *Proxy) accessibleCheck() (bool, error) {
	if p.Type == ProxyTypeSocks5 {
		return p.accessibleCheckForSocks5()
	}

	client := &http.Client{Timeout: 5 * time.Second}
	req, _ := http.NewRequest("GET", fmt.Sprintf("https://%s", p.GetDomain()), nil)

	timeStart := time.Now()
	resp, err := client.Do(req)
	if err != nil {
		p.Status = ProxyStatusFail
		p.Accessible = false
		return false, err
	}
	defer resp.Body.Close()

	timeEnd := time.Now()
	p.RespTime = timeEnd.Sub(timeStart).Milliseconds()

	if resp.StatusCode != 200 {
		p.Status = ProxyStatusFail
		p.Accessible = false
		return false, fmt.Errorf("HTTP StatusCode: %d", resp.StatusCode)
	} else {
		p.Status = ProxyStatusOK
		p.Accessible = true
		return true, nil
	}
}

func (p *Proxy) accessibleCheckForSocks5() (bool, error) {
	// check if the proxy is accessible, for socks5 proxy, with username, password, ip, port
	proxyURL := fmt.Sprintf("socks5://%s:%s@%s:%d", p.Username, p.Password, p.IP, p.Port)

	// Parse the proxy URL
	proxyURLParsed, err := url.Parse(proxyURL)
	if err != nil {
		p.Status = ProxyStatusFail
		p.Accessible = false
		return false, fmt.Errorf("invalid proxy URL: %v", err)
	}

	// Create HTTP client with SOCKS5 proxy
	client := &http.Client{
		Timeout: 10 * time.Second,
		Transport: &http.Transport{
			Proxy: http.ProxyURL(proxyURLParsed),
		},
	}

	// Test the proxy by making a request to a reliable test URL
	timeStart := time.Now()
	req, err := http.NewRequest("GET", "https://httpbin.org/ip", nil)
	if err != nil {
		p.Status = ProxyStatusFail
		p.Accessible = false
		return false, fmt.Errorf("failed to create request: %v", err)
	}

	resp, err := client.Do(req)
	if err != nil {
		p.Status = ProxyStatusFail
		p.Accessible = false
		return false, fmt.Errorf("proxy connection failed: %v", err)
	}
	defer resp.Body.Close()

	timeEnd := time.Now()
	p.RespTime = timeEnd.Sub(timeStart).Milliseconds()

	if resp.StatusCode != 200 {
		p.Status = ProxyStatusFail
		p.Accessible = false
		return false, fmt.Errorf("HTTP StatusCode: %d", resp.StatusCode)
	}

	// Read response body to ensure complete connection
	_, err = io.ReadAll(resp.Body)
	if err != nil {
		p.Status = ProxyStatusFail
		p.Accessible = false
		return false, fmt.Errorf("failed to read response: %v", err)
	}

	p.Status = ProxyStatusOK
	p.Accessible = true
	return true, nil
}

func (p *Proxy) createInstance() error {
	if p.Type != ProxyTypeDefault {
		return fmt.Errorf("only default proxy support create instance")
	}

	if p.template.Service == ServiceAWS {
		return p.createAWSInstance()
	}

	if p.template.Service == ServiceGOOGLE {
		return p.createGoogleInstance()
	}

	return fmt.Errorf("unsupported service: %s", p.template.Service)
}

func (p *Proxy) createDNSRecord() error {
	if p.Type != ProxyTypeDefault {
		return fmt.Errorf("only default proxy support create DNS record")
	}

	client := &http.Client{}
	data := map[string]interface{}{
		"type":    "A",
		"name":    p.GetDomain(),
		"content": p.IP,
	}
	jsonData, _ := json.Marshal(data)

	zoneID, err := p.template.CloudFlareAccount.GetZoneID(p.Domain)
	if err != nil {
		return err
	}
	req, _ := http.NewRequest("POST", "https://api.cloudflare.com/client/v4/zones/"+zoneID+"/dns_records", bytes.NewBuffer(jsonData))
	req.Header.Add("Authorization", "Bearer "+p.template.CloudFlareAccount.APIToken)
	req.Header.Add("Content-Type", "application/json")

	resp, err := client.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	body, _ := io.ReadAll(resp.Body)
	res := map[string]interface{}{}
	json.Unmarshal(body, &res)
	if res["success"] != true {
		return fmt.Errorf("API request failed: %v", res)
	}

	p.DNSOK = true
	return nil
}

func (p *Proxy) deleteDNSRecord() error {
	if p.Type != ProxyTypeDefault {
		return fmt.Errorf("only default proxy support delete DNS record")
	}

	records, err := p.template.CloudFlareAccount.GetDNSRecords()
	if err != nil {
		return err
	}

	for _, record := range records {
		if record.Name == p.GetDomain() {
			zoneID, err := p.template.CloudFlareAccount.GetZoneID(p.Domain)
			if err != nil {
				return err
			}

			req, _ := http.NewRequest("DELETE", fmt.Sprintf("https://api.cloudflare.com/client/v4/zones/%s/dns_records/%s", zoneID, record.ID), nil)
			req.Header.Add("Authorization", "Bearer "+p.template.CloudFlareAccount.APIToken)
			req.Header.Add("Content-Type", "application/json")

			client := &http.Client{}
			resp, err := client.Do(req)
			if err != nil {
				return err
			}
			defer resp.Body.Close()

			body, _ := io.ReadAll(resp.Body)
			var res struct {
				Success bool `json:"success"`
			}
			if err := json.Unmarshal(body, &res); err != nil {
				return fmt.Errorf("parse JSON response failed: %v", err)
			}

			if !res.Success {
				return fmt.Errorf("API request failed: %v", res)
			}
		}
	}

	p.DNSOK = false

	return nil
}

func (p *Proxy) InstallTrojan() error {
	if p.Type != ProxyTypeDefault {
		return fmt.Errorf("only default proxy support install Trojan")
	}

	keyPath := p.template.GetKeyPath()

	commands := []string{
		"sudo docker volume create trojan_data",
		// fmt.Sprintf("sudo docker run -d --name trojan -p 80:80/tcp -p 443:443/tcp -p 443:443/udp -e DOMAIN=%s -e PASSWORD=%s -v trojan_data:/root/.local/share/caddy --restart unless-stopped monlor/quick-trojan-go:main", p.GetDomain(), p.Password),

		// 使用 host 网络模式，这样可以直接使用宿主机的 WireGuard IPv6 网络
		fmt.Sprintf("sudo docker run -d --name trojan --network=\"host\" -e DOMAIN=%s -e PASSWORD=%s -v trojan_data:/root/.local/share/caddy --log-opt max-size=30m --log-opt max-file=3 --restart unless-stopped monlor/quick-trojan-go:main", p.GetDomain(), p.Password),
	}

	for i := 0; i < 3; i++ {
		time.Sleep(10 * time.Second) // 稍等一下，确保实例可以连接
		_, err := ExecuteSSHCommands(p.IP, "ubuntu", keyPath, commands)
		if err != nil {
			zlog.Errorf("安装 Trojan 失败: %v", err)
			if i < 9 {
				zlog.Infof("重试中...")
				continue
			}
			return err
		} else {
			break
		}
	}
	return nil
}

func (p *Proxy) RestartTrojan() error {
	if p.Type != ProxyTypeDefault {
		return fmt.Errorf("only default proxy support restart Trojan")
	}

	if p.template == nil {
		return fmt.Errorf("proxy template is nil")
	}

	keyPath := p.template.GetKeyPath()

	commands := []string{
		"sudo docker restart $(sudo docker ps -q)",
	}

	_, err := ExecuteSSHCommands(p.IP, "ubuntu", keyPath, commands)
	if err != nil {
		return fmt.Errorf("restart Trojan failed: %v", err)
	}
	return nil
}

func (p *Proxy) RemoveLogForTrojan() error {
	if p.Type != ProxyTypeDefault {
		return fmt.Errorf("only default proxy support remove log for Trojan")
	}

	keyPath := p.template.GetKeyPath()

	commands := []string{
		"sudo truncate -s 0 $(sudo docker inspect --format='{{.LogPath}}' trojan)",
		"sudo docker restart $(sudo docker ps -q)",
	}
	_, err := ExecuteSSHCommands(p.IP, "ubuntu", keyPath, commands)
	if err != nil {
		return fmt.Errorf("remove log for Trojan failed: %v", err)
	}
	return nil
}

func (p *Proxy) EnableWARP() error {
	if p.Type != ProxyTypeDefault {
		return fmt.Errorf("only default proxy support enable WARP")
	}

	keyPath := p.template.GetKeyPath()

	commands := []string{"sudo ./warp.sh rwg"}
	_, err := ExecuteSSHCommands(p.IP, "ubuntu", keyPath, commands)
	if err != nil {
		return fmt.Errorf("enable WARP failed: %v", err)
	}
	p.WARPEnabled = true
	return nil
}

func (p *Proxy) DisableWARP() error {
	if p.Type != ProxyTypeDefault {
		return fmt.Errorf("only default proxy support disable WARP")
	}

	keyPath := p.template.GetKeyPath()

	commands := []string{"sudo ./warp.sh dwg"}
	_, err := ExecuteSSHCommands(p.IP, "ubuntu", keyPath, commands)
	if err != nil {
		return fmt.Errorf("disable WARP failed: %v", err)
	}
	p.WARPEnabled = false
	return nil
}

func (p *Proxy) checkWARPStatus() error {
	if p.Type != ProxyTypeDefault {
		return fmt.Errorf("only default proxy support check WARP status")
	}

	keyPath := p.template.GetKeyPath()

	commands := []string{"sudo ./warp.sh status"}
	outputs, err := ExecuteSSHCommands(p.IP, "ubuntu", keyPath, commands)
	if err != nil {
		return fmt.Errorf("check WARP status failed: %v", err)
	}

	if len(outputs) != 1 {
		// zlog.Infof("warp status output: %v", outputs)
		return fmt.Errorf("check WARP status failed: unexpected output")
	}

	enabled := false
	output := strings.ReplaceAll(outputs[0], " ", "")
	if strings.Contains(output, "IPv6Network\t:\x1b[32mWARP") {
		enabled = true
	}

	if p.WARPEnabled != enabled {
		return fmt.Errorf("WARP status inconsistent, local: %v, actual: %v", p.WARPEnabled, enabled)
	}

	return nil

}

func randomPassword(length int) string {
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	seededRand := rand.New(rand.NewSource(time.Now().UnixNano()))
	b := make([]byte, length)
	for i := range b {
		b[i] = charset[seededRand.Intn(len(charset))]
	}
	return string(b)
}

// ExecuteSSHCommands 连接到指定的 SSH 服务器并执行命令
func ExecuteSSHCommands(ip, user, privateKeyPath string, commands []string) (outputs []string, err error) {
	// 读取私钥文件
	key, err := os.ReadFile(privateKeyPath)
	if err != nil {
		return nil, fmt.Errorf("unable to read private key: %v", err)
	}

	// 解析私钥
	signer, err := ssh.ParsePrivateKey(key)
	if err != nil {
		return nil, fmt.Errorf("unable to parse private key: %v", err)
	}

	// 创建 SSH 客户端配置
	config := &ssh.ClientConfig{
		User: user,
		Auth: []ssh.AuthMethod{
			ssh.PublicKeys(signer),
		},
		HostKeyCallback: ssh.InsecureIgnoreHostKey(),
	}

	// 连接到 SSH 服务器
	client, err := ssh.Dial("tcp", ip+":22", config)
	if err != nil {
		return nil, fmt.Errorf("failed to dial: %v", err)
	}
	defer client.Close()

	// 对每个命令执行
	for _, cmd := range commands {
		session, err := client.NewSession()
		if err != nil {
			return outputs, fmt.Errorf("failed to create session: %v", err)
		}
		defer session.Close()

		var stdout, stderr bytes.Buffer
		session.Stdout = &stdout
		session.Stderr = &stderr

		// 执行命令
		zlog.Debugf("run cmd \"%s\" @%s", cmd, ip)
		err = session.Run(cmd)
		if err != nil {
			return outputs, fmt.Errorf("failed to run command: %v, stderr: %s", err, stderr.String())
		}
		output := stdout.String()
		outputs = append(outputs, output)
		zlog.Debugf("cmd \"%s\" output: \n%s\n", cmd, output)
	}

	return outputs, nil
}
