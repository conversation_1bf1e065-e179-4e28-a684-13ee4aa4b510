package pgate

import (
	"fmt"
	"os"

	"github.com/wizhodl/quanter/common/zlog"
	"github.com/wizhodl/quanter/secrets"
	"gopkg.in/yaml.v2"
)

type Service string

const (
	ServiceAWS    Service = "aws"
	ServiceGOOGLE Service = "google"
)

type AWSAccount struct {
	ID                       string               `yaml:"id"`
	AccessKeyID              string               `yaml:"access_key_id"`
	SecretAccessKeyEncrypted string               `yaml:"secret_access_key_encrypted"`
	SecretAccessKey          secrets.SecretString `yaml:"secret_access_key"`
}

type FilePath string

func (k *FilePath) String() string {
	return string(*k)
}

func (k *FilePath) IsExist() bool {
	_, err := os.Stat(k.String())
	return err == nil
}

type SSHKey struct {
	ID      string   `yaml:"id"`
	KeyName string   `yaml:"key_name"`
	KeyPath FilePath `yaml:"key_path"`
}

type AWSTemplate struct {
	ID              string `yaml:"id"`
	AccountID       string `yaml:"account_id"`
	AMI             string `yaml:"ami"`
	Region          string `yaml:"region"`
	InstanceType    string `yaml:"instance_type"`
	SecurityGroupID string `yaml:"security_group_id"`
	SSHKeyID        string `yaml:"ssh_key_id"`

	Account *AWSAccount `yaml:"-"`
	SSHKey  *SSHKey     `yaml:"-"`
}

type GoogleAccount struct {
	ID                          string               `yaml:"id"`
	ProjectID                   string               `yaml:"project_id"`
	ServiceAccountJson          secrets.SecretString `yaml:"service_account_json"`
	ServiceAccountJsonEncrypted string               `yaml:"service_account_json_encrypted"`
}

type GoogleTemplate struct {
	ID          string `yaml:"id"`
	AccountID   string `yaml:"account_id"`
	SourceImage string `yaml:"source_image"`
	Zone        string `yaml:"zone"`
	MachineType string `yaml:"machine_type"`
	SSHKeyID    string `yaml:"ssh_key_id"`

	Account *GoogleAccount `yaml:"-"`
	SSHKey  *SSHKey        `yaml:"-"`
}

type CloudFlareAccount struct {
	ID       string             `yaml:"id"`
	APIToken string             `yaml:"api_token"`
	Domains  []CloudFlareDomain `yaml:"domains"`
}

type CloudFlareDomain struct {
	ZoneID string `yaml:"zone_id"`
	Domain string `yaml:"domain"`
}

type CloudFlareDNSRecord struct {
	ID      string `json:"id"`
	Type    string `json:"type"`
	Name    string `json:"name"`
	Content string `json:"content"`
	TTL     int    `json:"ttl"`
}

type ServiceInstance struct {
	Service Service       `json:"service"`
	ID      string        `json:"id"`
	Name    string        `json:"name"`
	IP      string        `json:"ip"`
	Zone    string        `json:"zone"`
	State   InstanceState `json:"state"`
}

type ProxyTemplate struct {
	Name                string   `yaml:"name"`
	Service             Service  `yaml:"service"`
	MachineTemplateID   string   `yaml:"machine_template_id"`
	CloudFlareAccountID string   `yaml:"cloudflare_account_id"`
	Domains             []string `yaml:"domains"` // 如果设置多个域名，随机选择一个
	EnableWARP          bool     `yaml:"enable_warp"`

	MachineTemplate   any                `yaml:"-"`
	CloudFlareAccount *CloudFlareAccount `yaml:"-"`
}

func (this *ProxyTemplate) GetKeyPath() (keyPath string) {
	if this.MachineTemplate == nil {
		return
	}
	if this.Service == ServiceAWS {
		awsTemplate := this.MachineTemplate.(*AWSTemplate)
		keyPath = awsTemplate.SSHKey.KeyPath.String()
	} else if this.Service == ServiceGOOGLE {
		googleTemplate := this.MachineTemplate.(*GoogleTemplate)
		keyPath = googleTemplate.SSHKey.KeyPath.String()
	}
	return
}

type GatewayServerOption struct {
	Enabled         bool     `yaml:"enabled"`
	Port            int      `yaml:"port"`
	EnableSurge     bool     `yaml:"enable_surge"`
	SurgeConfigPath FilePath `yaml:"surge_config_path"`
	SurgeReloadCmd  string   `yaml:"surge_reload_cmd"`
}

type SyncStorageAWSConfig struct {
	AccountID   string      `yaml:"account_id"`
	Region      string      `yaml:"region"`
	Bucket      string      `yaml:"bucket"`
	FromPgateID string      `yaml:"from_pgate_id"`
	Account     *AWSAccount `yaml:"-"`
}

type ProxyOptions struct {
	ID                              string                `yaml:"id"`
	Debug                           bool                  `yaml:"debug"`
	AWSAccounts                     []*AWSAccount         `yaml:"aws_accounts"`
	AWSTemplates                    []*AWSTemplate        `yaml:"aws_templates"`
	GoogleAccounts                  []*GoogleAccount      `yaml:"google_accounts"`
	GoogleTemplates                 []*GoogleTemplate     `yaml:"google_templates"`
	SSHKeys                         []*SSHKey             `yaml:"ssh_keys"`
	CloudFlareAccounts              []*CloudFlareAccount  `yaml:"cloudflare_accounts"`
	Templates                       []*ProxyTemplate      `yaml:"templates"`
	DataDir                         string                `yaml:"data_dir"`
	GatewayServer                   *GatewayServerOption  `yaml:"gateway_server"`
	SyncStorage                     *SyncStorageAWSConfig `yaml:"sync_storage"`
	ProxyAutoRestartIntervalMinutes int                   `yaml:"proxy_auto_restart_interval_minutes"`
}

func (o *ProxyOptions) Load(path string) error {
	file, err := os.Open(path)
	if err != nil {
		return err
	}
	defer file.Close()

	err = yaml.NewDecoder(file).Decode(o)
	if err != nil {
		return err
	}

	// 解密敏感信息
	// 在 -server 模式下会提示输入 password
	for _, a := range o.AWSAccounts {
		if a.SecretAccessKey == "" && a.SecretAccessKeyEncrypted != "" {
			if secrets.HasPassword() {
				a.SecretAccessKey, err = secrets.Decrypt(a.SecretAccessKeyEncrypted)
				if err != nil {
					return fmt.Errorf("decrypt AWS secret key failed: %v, make sure aws_account.SecretAccessKeyEncrypted is encrypted", err)
				} else {
					zlog.Infof("aws_account.SecretAccessKey decrypted successfully")
				}
			} else {
				return fmt.Errorf("password not set, can't decrypt AWS secret")
			}
		} else {
			zlog.Infof("use raw value aws_account.SecretAccessKey")
		}
	}

	for _, g := range o.GoogleAccounts {
		if g.ServiceAccountJson == "" && g.ServiceAccountJsonEncrypted != "" {
			if secrets.HasPassword() {
				g.ServiceAccountJson, err = secrets.Decrypt(g.ServiceAccountJsonEncrypted)
				if err != nil {
					return fmt.Errorf("decrypt Google service account json failed: %v, make sure google_account.ServiceAccountJsonEncrypted is encrypted", err)
				} else {
					zlog.Infof("google_account.ServiceAccountJson decrypted successfully")
				}
			} else {
				return fmt.Errorf("password not set, can't decrypt Google service account json")
			}
		} else {
			zlog.Infof("use raw value google_account.ServiceAccountJson")
		}
	}

	err = o.InitOptions()
	if err != nil {
		return err
	}

	return nil
}

func (o *ProxyOptions) InitOptions() error {
	if o.ID == "" {
		return fmt.Errorf("ID is required")
	}

	for _, k := range o.SSHKeys {
		if !k.KeyPath.IsExist() {
			return fmt.Errorf("SSH Key not found: %s", k.KeyPath)
		}
	}

	for _, t := range o.AWSTemplates {
		for _, k := range o.SSHKeys {
			if t.SSHKeyID == k.ID {
				t.SSHKey = k
				break
			}
		}

		if t.SSHKey == nil {
			return fmt.Errorf("AWS 模板 的 SSH Key 不存在, ID: %s, KeyID: %s", t.ID, t.SSHKeyID)
		}

		for _, a := range o.AWSAccounts {
			if t.AccountID == a.ID {
				t.Account = a
				break
			}
		}
	}

	for _, t := range o.GoogleTemplates {
		for _, k := range o.SSHKeys {
			if t.SSHKeyID == k.ID {
				t.SSHKey = k
				break
			}
		}

		if t.SSHKey == nil {
			return fmt.Errorf("Google 模板 的 SSH Key 不存在, ID: %s, KeyID: %s", t.ID, t.SSHKeyID)
		}

		for _, a := range o.GoogleAccounts {
			if t.AccountID == a.ID {
				t.Account = a
				break
			}
		}
	}

	for _, t := range o.Templates {
		for _, m := range o.AWSTemplates {
			if t.MachineTemplateID == m.ID && t.Service == ServiceAWS {
				t.MachineTemplate = m
				break
			}
		}

		for _, m := range o.GoogleTemplates {
			if t.MachineTemplateID == m.ID && t.Service == ServiceGOOGLE {
				t.MachineTemplate = m
				break
			}
		}

		if t.MachineTemplate == nil {
			return fmt.Errorf("模板的机器模板不存在, Name: %s, MachineTemplateID: %s", t.Name, t.MachineTemplateID)
		}

		for _, a := range o.CloudFlareAccounts {
			if t.CloudFlareAccountID == a.ID {
				t.CloudFlareAccount = a
				break
			}
		}

		if t.CloudFlareAccount == nil {
			return fmt.Errorf("模板的 Cloudflare 账号不存在, Name: %s, AccountID: %s", t.Name, t.CloudFlareAccountID)
		}

		for _, d := range t.Domains {
			found := false
			for _, a := range t.CloudFlareAccount.Domains {
				if d == a.Domain {
					found = true
					break
				}
			}
			if !found {
				return fmt.Errorf("模板的域名 %s 不存在于 Cloudflare 账号 %s 中", d, t.CloudFlareAccount.ID)
			}
		}
	}

	if o.SyncStorage != nil {
		for _, a := range o.AWSAccounts {
			if a.ID == o.SyncStorage.AccountID {
				o.SyncStorage.Account = a
				break
			}
		}
	}

	return nil
}

func (o *ProxyOptions) Validate() error {
	for _, t := range o.AWSTemplates {
		if err := t.Validate(); err != nil {
			return fmt.Errorf("AWS 模板 %s 验证失败: %v", t.ID, err)
		} else {
			zlog.Infof("AWS 模板 %s 验证成功", t.ID)
		}
	}

	for _, t := range o.GoogleTemplates {
		if err := t.Validate(); err != nil {
			return fmt.Errorf("Google 模板 %s 验证失败: %v", t.ID, err)
		} else {
			zlog.Infof("Google 模板 %s 验证成功", t.ID)
		}
	}

	for _, a := range o.CloudFlareAccounts {
		if err := a.Validate(); err != nil {
			return fmt.Errorf("Cloudflare 账号 %s 验证失败: %v", a.ID, err)
		} else {
			zlog.Infof("Cloudflare 账号 %s 验证成功", a.ID)
		}
	}

	return nil
}

func (o *ProxyOptions) GetTemplate(id string) (template *ProxyTemplate) {
	for _, t := range o.Templates {
		if t.Name == id {
			return t
		}
	}
	return nil
}
