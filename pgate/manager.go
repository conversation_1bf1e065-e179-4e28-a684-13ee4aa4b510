package pgate

import (
	"encoding/json"
	"fmt"
	"io"
	"math/rand"
	"os"
	"path"
	"path/filepath"
	"sort"
	"strings"
	"sync"
	"time"

	"github.com/fatih/color"
	"github.com/spf13/cast"
	"github.com/tidwall/gjson"
	"github.com/wizhodl/quanter/common/command"
	"github.com/wizhodl/quanter/common/zlog"
	"github.com/wizhodl/quanter/utils"
)

const STORAGE_FILE = "proxy_storage.json"

type ProxyManager struct {
	Proxies            []*Proxy                   `json:"proxies"`
	ProxyLogins        map[string]*ProxyLoginBook `json:"proxy_logins"`         // key: proxy name
	GroupForwarders    map[string]*GroupForwarder `json:"group_forwarders"`     // key: group name
	ProjectGroupLimits map[string]int             `json:"project_group_limits"` // project::group -> accountLimit

	Options        *ProxyOptions `json:"-"`
	proxyiesMutex  sync.Mutex
	mutex          sync.Mutex
	storageFileMD5 string

	controller command.CommandProcessorResponder
	Gateway    *ProxyGateway `json:"-"`
}

func NewProxyManager(options *ProxyOptions, controller command.CommandProcessorResponder) (manager *ProxyManager, er error) {
	zlog.Infof("create proxy manager")

	manager = &ProxyManager{
		Options:    options,
		controller: controller,
	}
	go func() {
		time.Sleep(time.Second * 10) // 等数据加载完成执行一次检查

		err := options.Validate()
		if err != nil {
			manager.ErrorMsgf("validate options failed: %v", err)
		}

		manager.SyncProxyStatus()
		manager.CheckServerInstance()
		manager.CheckAWSReservedInstances()
		manager.CheckGoogleCommitments()
		manager.ClearStorage()

		ticker10min := time.NewTicker(time.Minute * 10)
		tickr12Hour := time.NewTicker(time.Hour * 12)

		restartProxyTicker := time.NewTicker(time.Minute * time.Duration(60*24*7)) // 默认一周重启一次
		if options.ProxyAutoRestartIntervalMinutes < 0 {
			restartProxyTicker = time.NewTicker(10 * 365 * 24 * time.Hour) // really long time
			zlog.Warnf("proxy auto restart interval minutes is less than 0, disable auto restart")
		} else if options.ProxyAutoRestartIntervalMinutes > 0 {
			restartProxyTicker = time.NewTicker(time.Minute * time.Duration(options.ProxyAutoRestartIntervalMinutes))
			zlog.Infof("proxy auto restart interval minutes is %d, enable auto restart", options.ProxyAutoRestartIntervalMinutes)
		}

		for {
			select {
			case <-ticker10min.C:
				manager.SyncProxyStatus()
				manager.CheckServerInstance()
			case <-tickr12Hour.C:
				manager.CheckAWSReservedInstances()
				manager.CheckGoogleCommitments()
				manager.ClearStorage()
			case <-restartProxyTicker.C:
				manager.RestartProxies()
			}
		}
	}()

	err := manager.load()
	if err != nil {
		return nil, fmt.Errorf("load storage failed: %v", err)
	}

	if manager.ProjectGroupLimits == nil {
		manager.ProjectGroupLimits = map[string]int{}
	}

	if manager.ProxyLogins == nil {
		manager.ProxyLogins = map[string]*ProxyLoginBook{}
	}

	if manager.GroupForwarders == nil {
		manager.GroupForwarders = map[string]*GroupForwarder{}
	}

	return
}

func (m *ProxyManager) GetProxies() []*Proxy {
	m.proxyiesMutex.Lock()
	defer m.proxyiesMutex.Unlock()
	copyProxies := make([]*Proxy, len(m.Proxies))
	copy(copyProxies, m.Proxies)
	return copyProxies
}

func (m *ProxyManager) SetProxies(proxies []*Proxy) {
	m.proxyiesMutex.Lock()
	defer m.proxyiesMutex.Unlock()
	m.Proxies = proxies
}

func (m *ProxyManager) AddProxy(proxy *Proxy) {
	m.proxyiesMutex.Lock()
	defer m.proxyiesMutex.Unlock()
	m.Proxies = append(m.Proxies, proxy)
}

func (m *ProxyManager) RemoveProxy(name string) {
	m.proxyiesMutex.Lock()
	defer m.proxyiesMutex.Unlock()
	for i, p := range m.Proxies {
		if p.Name == name {
			m.Proxies = append(m.Proxies[:i], m.Proxies[i+1:]...)
			break
		}
	}
}

func (m *ProxyManager) SendMsgf(msg string, args ...any) {
	if m.controller != nil {
		m.controller.SendMsgf(msg, args...)
	} else {
		zlog.Infof(msg, args...)
	}
}

func (m *ProxyManager) ErrorMsgf(msg string, args ...any) {
	if m.controller != nil {
		m.controller.ErrorMsgf(msg, args...)
	} else {
		zlog.Errorf(msg, args...)
	}
}

func (m *ProxyManager) WarnMsgf(msg string, args ...any) {
	if m.controller != nil {
		m.controller.WarnMsgf(msg, args...)
	} else {
		zlog.Warnf(msg, args...)
	}
}

func (m *ProxyManager) AlertMsgf(msg string, args ...any) {
	if m.controller != nil {
		m.controller.AlertMsgf(msg, args...)
	} else {
		zlog.Errorf(msg, args...)
	}
}

func FilterActiveProxies(proxies []*Proxy) []*Proxy {
	activeProxies := []*Proxy{}
	for _, p := range proxies {
		if p.Status != ProxyStatusDeleted {
			activeProxies = append(activeProxies, p)
		}
	}
	return activeProxies
}

func (m *ProxyManager) ActiveProxies() []*Proxy {
	return FilterActiveProxies(m.GetProxies())
}

func (m *ProxyManager) ActiveSocks5Proxies() []*Proxy {
	proxies := []*Proxy{}
	for _, p := range m.ActiveProxies() {
		if p.Type == ProxyTypeSocks5 {
			proxies = append(proxies, p)
		}
	}
	return proxies
}

func (m *ProxyManager) ActiveDefaultProxies() []*Proxy {
	proxies := []*Proxy{}
	for _, p := range m.ActiveProxies() {
		if p.Type == ProxyTypeDefault {
			proxies = append(proxies, p)
		}
	}
	return proxies
}

func FilterDeletedProxies(proxies []*Proxy) []*Proxy {
	deletedProxies := []*Proxy{}
	for _, p := range proxies {
		if p.Status == ProxyStatusDeleted {
			deletedProxies = append(deletedProxies, p)
		}
	}
	return deletedProxies
}

func (m *ProxyManager) DeletedProxies() []*Proxy {
	return FilterDeletedProxies(m.GetProxies())
}

func (m *ProxyManager) GetTemplates() []*ProxyTemplate {
	return m.Options.Templates
}

func (m *ProxyManager) GetTemplateNames() []string {
	names := []string{}
	for _, t := range m.GetTemplates() {
		names = append(names, t.Name)
	}
	return names
}

func (m *ProxyManager) CreateProxyCallback(proxy *Proxy, err error) {
	if err != nil {
		m.ErrorMsgf("create proxy failed, name: %s, error: %v", proxy.Name, err)
	} else {
		m.SendMsgf("代理 %s 创建完成", proxy.Name)
	}

	if m.Gateway != nil {
		m.Gateway.RefreshProxies()
	}
}

func (m *ProxyManager) CreateProxy(name, templateID string, lifeHour int, group string) (proxy *Proxy, er error) {
	zlog.Infof("create proxy: %s, template: %s", name, templateID)

	if !isValidName(name) {
		return nil, fmt.Errorf("invalid name")
	}

	if err := CheckGroupName(group); err != nil {
		return nil, fmt.Errorf("invalid group name, %s", err)
	}

	if exist, err := m.IsProxyExist(name, templateID); err != nil {
		return nil, fmt.Errorf("check proxy exist failed: %v", err)
	} else if exist {
		return nil, fmt.Errorf("proxy already exist")
	}

	template := m.Options.GetTemplate(templateID)
	if template == nil {
		return nil, fmt.Errorf("template not found")
	}

	p := NewProxy(name, template, lifeHour, group)
	m.AddProxy(p)
	defer m.Save()

	go func() {
		err := p.Create()
		m.CreateProxyCallback(p, err)
		m.Save()
	}()

	return p, nil
}

// SuggestName 生成一个建议的名字，确保名字唯一
func (m *ProxyManager) SuggestName(templateID string) (name string) {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	template := m.Options.GetTemplate(templateID)
	namesInDNS := []string{}
	if template != nil {
		records, _ := template.CloudFlareAccount.GetDNSRecords()
		for _, r := range records {
			for _, templateDomain := range template.Domains {
				if r.Type == "A" && strings.HasSuffix(r.Name, templateDomain) && len(r.Name) > (len(templateDomain)+1) {
					name := r.Name[:len(r.Name)-len(templateDomain)-1]
					namesInDNS = append(namesInDNS, name)
				}
			}
		}
	}

	// 获取所有现有的名称
	existingNames := m.existingNames()
	// 获取还没有被使用的名字
	for _, n := range NameNouns {
		if !utils.SliceContains(existingNames, n) && !utils.SliceContains(namesInDNS, n) {
			name = n
			break
		}
	}

	// 如果没有找到还没有被使用的名字，就随机生成一个
	if name == "" {
		name = fmt.Sprintf("%s%03d", NameNouns[rand.Intn(len(NameNouns))], rand.Intn(1000))
	}
	return
}

func (m *ProxyManager) existingNames() []string {
	existingNames := []string{}
	for _, p := range m.GetProxies() {
		existingNames = append(existingNames, p.Name)
	}
	return existingNames
}

func (m *ProxyManager) DeleteProxyCallback(proxy *Proxy, err error) {
	if err != nil {
		m.ErrorMsgf("delete proxy failed, name: %s, error: %v", proxy.Name, err)
	} else {
		m.SendMsgf("代理 %s 删除完成", proxy.Name)
	}

	if m.Gateway != nil {
		m.Gateway.RefreshProxies()
	}
}

func (m *ProxyManager) DeleteProxy(name string) error {
	zlog.Infof("delete proxy: %s", name)
	p := m.GetProxyByName(name)
	if p == nil {
		return fmt.Errorf("proxy not found")
	}

	go func() {
		defer m.Save()
		err := p.Delete()
		m.DeleteProxyCallback(p, err)
	}()
	return nil
}

func (m *ProxyManager) RepairProxy(name string) error {
	zlog.Infof("repair proxy: %s", name)
	p := m.GetProxyByName(name)
	if p == nil {
		return fmt.Errorf("proxy not found")
	}

	if p.Status != ProxyStatusFail && p.Status != ProxyStatusOK {
		return fmt.Errorf("proxy status not fail or ok")
	}

	go func() {
		if err := p.Repair(); err != nil {
			m.ErrorMsgf("recover proxy failed, name: %s, error: %v", name, err)
		} else {
			m.SendMsgf("代理 %s 修复完成", name)
		}
		m.Save()
	}()

	return nil
}

func (m *ProxyManager) StopProxy(name string) error {
	zlog.Infof("stop proxy: %s", name)
	p := m.GetProxyByName(name)
	if p == nil {
		return fmt.Errorf("proxy not found")
	}

	go func() {
		defer m.Save()
		if err := p.Stop(); err != nil {
			m.ErrorMsgf("代理 %s 停止失败: %v", name, err)
		} else {
			m.SendMsgf("代理 %s 已停止", name)
		}
	}()

	return nil
}

func (m *ProxyManager) StartProxy(name string) error {
	zlog.Infof("start proxy: %s", name)
	p := m.GetProxyByName(name)
	if p == nil {
		return fmt.Errorf("proxy not found")
	}

	go func() {
		defer m.Save()
		if err := p.Start(); err != nil {
			m.ErrorMsgf("代理 %s 启动失败: %v", name, err)
		} else {
			m.SendMsgf("代理 %s 已启动", name)
		}
	}()

	return nil
}

func (m *ProxyManager) IncreaseLifeHour(name string, hour int) error {
	zlog.Infof("increase life hour: %s, %d", name, hour)
	p := m.GetProxyByName(name)
	if p == nil {
		return fmt.Errorf("proxy not found")
	}

	if (p.LifeHour + hour) == 0 {
		return fmt.Errorf("can not set life hour to 0")
	}

	p.LifeHour += hour
	m.Save()
	return nil
}

func (m *ProxyManager) Save() error {
	m.mutex.Lock()
	startTime := time.Now()
	defer func() {
		zlog.Infof("pgate save to storage took %s", time.Since(startTime))
		m.mutex.Unlock()
	}()

	if _, err := os.Stat(m.Options.DataDir); os.IsNotExist(err) {
		os.MkdirAll(m.Options.DataDir, os.ModePerm)
	}

	path := path.Join(m.Options.DataDir, STORAGE_FILE)

	// backup old storage file, if the old file is valid json
	oldData, err := os.ReadFile(path)
	if err == nil && gjson.Valid(string(oldData)) {
		backupPath := filepath.Join(m.Options.DataDir, STORAGE_FILE+".bak")
		if err := utils.CopyFile(path, backupPath); err != nil {
			zlog.Errorf("backup old storage file error: %s", err)
			return err
		}
	}

	if data, err := json.MarshalIndent(m, "", "    "); err != nil {
		zlog.Errorf("marshal storage failed, error: %s", err)
		return err
	} else {
		err = os.WriteFile(path, data, os.ModePerm)
		if err != nil {
			zlog.Errorf("save storage failed, error: %s", err)
			return err
		}
		go m.SyncStorage()
		return nil
	}
}

func (m *ProxyManager) load() error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	if _, err := os.Stat(m.Options.DataDir); os.IsNotExist(err) {
		os.MkdirAll(m.Options.DataDir, os.ModePerm)
	}

	path := path.Join(m.Options.DataDir, STORAGE_FILE)
	if _, err := os.Stat(path); os.IsNotExist(err) {
		return nil
	}

	f, _ := os.OpenFile(path, os.O_RDWR|os.O_CREATE, os.ModePerm)
	defer f.Close()
	file, err := io.ReadAll(f)
	if err != nil {
		zlog.Errorf("read storage file failed, error: %s", err)
		return err
	}

	if len(file) == 0 {
		zlog.Errorf("read storage content failed, zero bytes")
		return fmt.Errorf("read storage content zero bytes")
	}

	err = json.Unmarshal(file, m)
	if err != nil {
		zlog.Errorf("unmarshal storage file failed, error: %s", err)
		return err
	}

	// Only check templates for ProxyTypeDefault proxies
	validProxies := []*Proxy{}
	for _, p := range m.GetProxies() {
		if p.Type == ProxyTypeDefault {
			p.template = m.Options.GetTemplate(p.TemplateName)
			if p.template == nil {
				zlog.Warnf("proxy template not found for %s, template: %s, removing from storage", p.Name, p.TemplateName)
				continue
			}
		}
		// For SOCKS5 proxies, no template is needed
		validProxies = append(validProxies, p)
	}

	// Update the proxies list with only valid ones
	m.SetProxies(validProxies)

	return nil
}

func (m *ProxyManager) IsProxyExist(name, templateID string) (bool, error) {
	for _, p := range m.GetProxies() {
		if p.Name == name {
			return true, nil
		}
	}

	template := m.Options.GetTemplate(templateID)
	if template == nil {
		return false, fmt.Errorf("template not found")
	}

	dnsNames := []string{}
	for _, domain := range template.Domains {
		dnsNames = append(dnsNames, fmt.Sprintf("%s.%s", name, domain))
	}

	if exit, err := template.CloudFlareAccount.DNSRecordExists(dnsNames); err != nil {
		return false, fmt.Errorf("check DNS record failed: %v", err)
	} else if exit {
		return true, nil
	}

	return false, nil
}

func (m *ProxyManager) GetProxyInstances() (instances []ServiceInstance) {
	for _, awsTmp := range m.Options.AWSTemplates {
		awsInstances, err := awsTmp.ListProxyInstances()
		if err != nil {
			zlog.Errorf("获取 AWS 实例失败: %v", err)
			return
		}

		for _, instance := range awsInstances {
			name := "unknown"
			for _, tag := range instance.Tags {
				if *tag.Key == "Name" {
					name = *tag.Value
					break
				}
			}

			ip := ""
			if instance.PublicIpAddress != nil {
				ip = *instance.PublicIpAddress
			}

			instances = append(instances, ServiceInstance{
				Service: ServiceAWS,
				ID:      *instance.InstanceId,
				Name:    name,
				IP:      ip,
				Zone:    awsTmp.Region,
				State:   convertAWSInstanceState(instance.State.Name),
			})
		}
	}

	for _, googleTmp := range m.Options.GoogleTemplates {
		googleInstances, err := googleTmp.ListProxyInstances()
		if err != nil {
			zlog.Errorf("获取 Google 实例失败: %v", err)
			return
		}

		for _, instance := range googleInstances {
			ip := ""
			for _, ni := range instance.GetNetworkInterfaces() {
				for _, ac := range ni.GetAccessConfigs() {
					if ac.GetNatIP() != "" {
						ip = ac.GetNatIP()
						break
					}
				}
			}

			instances = append(instances, ServiceInstance{
				Service: ServiceGOOGLE,
				ID:      fmt.Sprintf("%d", instance.GetId()),
				Name:    instance.GetName(),
				IP:      ip,
				Zone:    googleTmp.Zone,
				State:   convertGoogleInstanceState(instance.GetStatus()),
			})
		}
	}

	return instances
}

func (m *ProxyManager) SyncProxyStatus() {
	if len(m.Options.Templates) == 0 || len(m.GetProxies()) == 0 {
		return
	}

	proxyInstances := m.GetProxyInstances()

	dnsRecords := map[string]CloudFlareDNSRecord{}
	for _, cf := range m.Options.CloudFlareAccounts {
		records, err := cf.GetDNSRecords()
		if err != nil {
			zlog.Errorf("获取 DNS 记录失败: %v", err)
			return
		}
		for _, r := range records {
			dnsRecords[r.Name] = r
		}
	}

	for _, p := range m.ActiveDefaultProxies() {
		if p.IsLifeOver() {
			m.SendMsgf("代理 %s 生命周期已结束，删除中", p.Name)
			m.DeleteProxy(p.Name)
			continue
		}

		if p.Status != ProxyStatusOK && p.Status != ProxyStatusFail {
			// 非最终状态的代理不需要检查
			continue
		}

		instanceFound := false
		for _, i := range proxyInstances {
			if i.ID == p.InstanceID && i.Service == p.template.Service {
				instanceFound = true
				p.InstanceState = i.State
				p.InstanceIP = i.IP
				if p.IP != p.InstanceIP {
					p.Status = ProxyStatusFail
				}
				break
			}
		}

		if !instanceFound {
			p.InstanceState = InstanceStateNotFound
			p.InstanceIP = ""
			p.DNSOK = false
			p.Status = ProxyStatusFail
			continue
		}

		dnsRecord, dnsRecordFound := dnsRecords[p.GetDomain()]
		if !dnsRecordFound || dnsRecord.Content != p.IP {
			p.DNSOK = false
			p.Status = ProxyStatusFail
		} else {
			p.DNSOK = true
		}

		if err := p.checkWARPStatus(); err != nil {
			if m.controller != nil {
				m.AlertMsgf("check WARP status error, name: %s, error: %v", p.Name, err)
			}
		}

		p.accessibleCheck()
	}

	m.Save()
}

func (m *ProxyManager) CheckServerInstance() {
	proxyInstances := m.GetProxyInstances()
	for _, instance := range proxyInstances {
		// if instance not in proxies, alert
		found := false
		for _, p := range m.ActiveDefaultProxies() {
			if p.InstanceID == instance.ID && p.template.Service == instance.Service {
				found = true
				break
			}
		}
		if !found {
			state := instance.State
			if state == InstanceStateTerminated {
				continue
			}

			m.AlertMsgf("%s proxy instance not found in local proxies, region: %s, id: %s, name: %s, state: %s", instance.Service, instance.Zone, instance.ID, instance.Name, state)
		}
	}
}

// 预留实例到期检查，在 48 小时以内到期的预留实例会发送警告
func (m *ProxyManager) CheckAWSReservedInstances() {
	awsReservedInstances, err := m.GetAWSReservedInstances()
	if err != nil {
		zlog.Errorf("获取 AWS 预留实例失败: %v", err)
		return
	}

	for account, instances := range awsReservedInstances {
		for _, instance := range instances {
			if time.Until(*instance.End) < time.Hour*48 {
				m.WarnMsgf("AWS 预留实例即将到期, 账号: %s, 区域: %s, 预留实例: %s, 到期时间: %s",
					account, instance.Region, instance.InstanceType, instance.End.Format("2006-01-02 15:04"))
			}
		}
	}
}

func (m *ProxyManager) CheckGoogleCommitments() {
	googleCommitments, err := m.GetGoogleCommitments()
	if err != nil {
		zlog.Errorf("获取 Google Commitments 失败: %v", err)
		return
	}

	for account, commitments := range googleCommitments {
		for _, c := range commitments {
			endTime, _ := time.Parse(time.RFC3339Nano, *c.EndTimestamp)
			if time.Until(endTime) < time.Hour*48 {
				m.WarnMsgf("Google Commitment 即将到期, 账号: %s, 区域: %s, 名称: %s, 到期时间: %s",
					account, c.Region, *c.Name, endTime.Format("2006-01-02 15:04"))
			}
		}
	}
}

func (m *ProxyManager) TestAllProxies() {
	var wg sync.WaitGroup

	for _, p := range m.ActiveProxies() {
		if p.Status != ProxyStatusOK && p.Status != ProxyStatusFail {
			// 非最终状态的代理不需要检查
			continue
		}

		wg.Add(1)
		go func(p *Proxy) {
			defer wg.Done()
			p.accessibleCheckLoop()
		}(p)
	}

	wg.Wait()

	m.Save()

	if m.Gateway != nil {
		m.Gateway.RefreshProxies()
	}
}

func FormatProxies(proxies []*Proxy, withColor bool) string {
	result := "[no proxies]"
	if len(proxies) == 0 {
		return result
	}

	t := utils.NewTable()
	t.SetHeader([]string{"No.", "Name", "Type", "Group", "Address", "Auth", "Status", "WARP", "InstanceID", "InstanceState", "IP", "DNS", "Accessible", "Life Time"})
	for i, p := range proxies {
		var proxyType, auth, address string

		if p.Type == ProxyTypeSocks5 {
			proxyType = "SOCKS5"
			auth = fmt.Sprintf("%s:%s", p.Username, p.Password)
			address = p.GetAddr()
		} else {
			proxyType = p.TemplateName
			auth = p.Password
			address = p.GetDomain()
		}

		DNSOK := "OK"
		if !p.DNSOK {
			DNSOK = "NOT FOUND"
		}
		ip := p.IP
		if ip == "" {
			ip = "-"
		} else if p.IP != p.InstanceIP {
			ip = fmt.Sprintf("%s (%s)", p.IP, p.InstanceIP)
		}
		status := string(p.Status)
		if withColor {
			status = p.Status.ColoredString()
		}

		if p.Status == ProxyStatusOK {
			status = fmt.Sprintf("%s (%d ms)", status, p.RespTime)
		}

		life := "-"
		if p.LifeHour != 0 {
			leftHour := time.Until(p.CreatedAt.Add(time.Duration(p.LifeHour) * time.Hour)).Hours()
			if leftHour >= 0 {
				life = fmt.Sprintf("-%.1f h (%dh)", leftHour, p.LifeHour)
			} else {
				life = fmt.Sprintf("over (%dh)", p.LifeHour)
			}
		}

		warpEnabled := fmt.Sprintf("%v", p.WARPEnabled)
		instanceID := p.InstanceID
		instanceState := string(p.InstanceState)
		if p.Type == ProxyTypeSocks5 {
			warpEnabled = "N/A"
			instanceID = "N/A"
			instanceState = "N/A"
			DNSOK = "N/A"
			ip = "N/A"
		}

		t.AddRow([]string{
			fmt.Sprintf("%d", i+1),
			p.Name,
			proxyType,
			p.Group,
			address,
			auth,
			status,
			warpEnabled,
			instanceID,
			instanceState,
			ip,
			DNSOK,
			fmt.Sprintf("%v", p.Accessible),
			life,
		})
	}
	result = t.Render()
	failedProxies := []string{}
	for _, p := range proxies {
		if p.Status == ProxyStatusFail {
			failedProxies = append(failedProxies, p.Name)
		}
	}
	if len(failedProxies) > 0 {
		result += color.YellowString("\n\nFailed Proxies %d:\n", len(failedProxies))
		result += fmt.Sprintf("%s\n", strings.Join(failedProxies, ", "))
	}
	return result
}

func (m *ProxyManager) PrintProxies(color bool) string {
	return FormatProxies(m.ActiveProxies(), color)
}

func (m *ProxyManager) GetProxyByName(name string) *Proxy {
	for _, p := range m.ActiveProxies() {
		if p.Name == name {
			return p
		}
	}
	return nil
}

func FormatDeletedProxies(proxies []*Proxy, color bool) string {
	result := "[no deleted proxies]"
	if len(proxies) == 0 {
		return result
	}

	t := utils.NewTable()
	t.SetHeader([]string{"No.", "Name", "Template", "Domain", "InstanceID", "IP", "CreatedAt", "DeletedAt"})
	for i, p := range proxies {
		t.AddRow([]string{
			fmt.Sprintf("%d", i+1),
			p.Name,
			p.TemplateName,
			p.GetDomain(),
			p.InstanceID,
			p.IP,
			p.CreatedAt.Format("2006-01-02 15:04:05"),
			p.DeletedAt.Format("2006-01-02 15:04:05"),
		})
	}
	return t.Render()
}

func (m *ProxyManager) PrintDeletedProxies(color bool) string {
	return FormatDeletedProxies(m.DeletedProxies(), color)
}

func FormatLoginForwarders(proxyLogins map[string]*ProxyLoginBook, withColor bool) string {
	t := utils.NewTable()
	result := "[no login forwarders]"
	t.SetHeader([]string{"", "UID", "Project", "Forwarder Port", "Forwarder Status", "Proxy Name", "Proxy Status"})
	rows := [][]string{}

	unusedProxies := []string{}
	for _, loginBook := range proxyLogins {
		if len(loginBook.Accounts) == 0 {
			unusedProxies = append(unusedProxies, loginBook.ProxyName)
		}
		for _, account := range loginBook.Accounts {
			if account.LastActive != nil {
				proxyStatusStr := ""
				forwarderStatus := ""
				forwarderPort := ""
				proxyName := ""
				if loginBook.Proxy != nil {
					proxyStatusStr = string(loginBook.Proxy.Status)
					proxyName = loginBook.Proxy.Name
					if withColor {
						proxyStatusStr = loginBook.Proxy.Status.ColoredString()
					}
				}
				if loginBook.Forwarder != nil {
					forwarderStatus = string(loginBook.Forwarder.Status)
					if withColor {
						forwarderStatus = color.GreenString(forwarderStatus)
					}
					forwarderPort = fmt.Sprintf("%d", loginBook.Forwarder.Port)
				}
				rows = append(rows, []string{"", account.UID, account.Project, forwarderPort, forwarderStatus, proxyName, proxyStatusStr})
			}
		}
	}
	// sort by project
	sort.Slice(rows, func(i, j int) bool {
		return rows[i][2] < rows[j][2]
	})

	for i, row := range rows {
		row[0] = fmt.Sprintf("%d", i+1)
		t.AddRow(row)
	}
	if len(rows) > 0 {
		result = t.Render()
	}

	if len(unusedProxies) > 0 {
		result += color.YellowString("\n\nUnused Proxies %d:\n", len(unusedProxies))
		result += fmt.Sprintf("%s\n", strings.Join(unusedProxies, ", "))
	}
	return result
}

func FormatForwarders(proxyLogins map[string]*ProxyLoginBook, withColor bool) string {
	t := utils.NewTable()
	result := "[no proxy forwarders]"
	t.SetHeader([]string{"", "Forwarder Port", "Forwarder Status", "Proxy Name", "Proxy Status"})
	rows := [][]string{}
	for _, loginBook := range proxyLogins {
		proxyStatus := ""
		forwarderStatus := ""
		proxyName := ""
		if loginBook.Proxy != nil {
			proxyStatus = string(loginBook.Proxy.Status)
			proxyName = loginBook.Proxy.Name
			if withColor {
				proxyStatus = loginBook.Proxy.Status.ColoredString()
			}
		}
		forwarderStatus = string(loginBook.Forwarder.Status)
		if withColor {
			forwarderStatus = color.GreenString(forwarderStatus)
		}
		rows = append(rows, []string{"", fmt.Sprintf("%d", loginBook.Forwarder.Port), forwarderStatus, proxyName, proxyStatus})
	}
	// sort by port
	sort.Slice(rows, func(i, j int) bool {
		return rows[i][1] < rows[j][1]
	})

	for i, row := range rows {
		row[0] = fmt.Sprintf("%d", i+1)
		t.AddRow(row)
	}
	if len(rows) > 0 {
		result = t.Render()
	}
	return result
}

func FormatGroupForwarders(groupForwarders map[string]*GroupForwarder, withColor bool) string {
	t := utils.NewTable()
	result := "[no group forwarders]"
	t.SetHeader([]string{"", "Group", "Forwarder Port", "Forwarder Status", "Proxies"})
	rows := [][]string{}
	for _, groupForwarder := range groupForwarders {
		forwarderStatus := string(groupForwarder.Status)
		if withColor {
			if groupForwarder.Status == ForwarderStatusOK {
				forwarderStatus = color.GreenString(forwarderStatus)
			} else {
				forwarderStatus = color.RedString(forwarderStatus)
			}
		}
		rows = append(rows, []string{
			"",
			groupForwarder.Group,
			fmt.Sprintf("%d", groupForwarder.Port),
			forwarderStatus,
			strings.Join(groupForwarder.Proxies, ", "),
		})
	}
	sort.Slice(rows, func(i, j int) bool {
		return rows[i][2] < rows[j][2]
	})

	for i, row := range rows {
		row[0] = fmt.Sprintf("%d", i+1)
		t.AddRow(row)
	}
	if len(rows) > 0 {
		result = t.Render()
	}
	return result
}

func (this *ProxyManager) FormatLoginForwarders(color bool) string {
	return FormatLoginForwarders(this.ProxyLogins, color)
}

func (this *ProxyManager) FormatGroupForwarders(color bool) string {
	return FormatGroupForwarders(this.GroupForwarders, color)
}

func (this *ProxyManager) FormatForwarders(color bool) string {
	return FormatForwarders(this.ProxyLogins, color)
}

func FormatAWSReservedInstances(reservedInstances []AWSReservedInstancesWithRegion) string {
	t := utils.NewTable()
	t.SetHeader([]string{"Region", "ID", "Type", "Count", "Product", "State", "Start", "End"})
	for _, instance := range reservedInstances {
		t.AddRow([]string{
			instance.Region,
			*instance.ReservedInstancesId,
			string(instance.InstanceType),
			fmt.Sprintf("%d", *instance.InstanceCount),
			string(instance.ProductDescription),
			string(instance.State),
			instance.Start.Format("2006-01-02"),
			instance.End.Format("2006-01-02"),
		})
	}
	return t.Render()
}

func FormatAWSReservationCoverage(coverages []AWSReservationCoverage) string {
	t := utils.NewTable()
	t.SetHeader([]string{"Region", "Instance Type", "Platform", "Uncovered Hours", "Reserved Hours", "Total Hours", "Coverage Percentage"})
	for _, c := range coverages {
		t.AddRow([]string{
			c.Attributes["region"],
			c.Attributes["instanceType"],
			c.Attributes["platform"],
			fmt.Sprintf("%.1f", cast.ToFloat64(c.Coverage.CoverageHours.OnDemandHours)),
			fmt.Sprintf("%.1f", cast.ToFloat64(c.Coverage.CoverageHours.ReservedHours)),
			fmt.Sprintf("%.1f", cast.ToFloat64(c.Coverage.CoverageHours.TotalRunningHours)),
			fmt.Sprintf("%.1f%%", cast.ToFloat64(c.Coverage.CoverageHours.CoverageHoursPercentage)),
		})
	}
	return t.Render()
}

func FormatGoogleCommittedUsageDiscounts(commits []*GoogleCommitment) string {
	t := utils.NewTable()
	t.SetHeader([]string{"Region", "Name", "Type", "CPUs", "MEM", "Status", "Start", "End", "AutoRenew"})
	for _, c := range commits {
		var cpu, mem int64
		for _, r := range c.Resources {
			if *r.Type == "VCPU" {
				cpu += *r.Amount
			} else if *r.Type == "MEMORY" {
				mem += *r.Amount
			}
		}

		startTime, _ := time.Parse(time.RFC3339Nano, *c.StartTimestamp)
		endTime, _ := time.Parse(time.RFC3339Nano, *c.EndTimestamp)

		t.AddRow([]string{
			c.Region,
			*c.Name,
			*c.Type,
			fmt.Sprintf("%d", cpu),
			fmt.Sprintf("%.2f GB", float64(mem)/1024),
			*c.Status,
			startTime.Format("2006-01-02"),
			endTime.Format("2006-01-02"),
			fmt.Sprintf("%v", *c.AutoRenew),
		})
	}
	return t.Render()
}

func FormatGoogleCommittedUsageDiscountsCoverage(coverages []*GoogleCommitmentCoverage) string {
	t := utils.NewTable()
	t.SetHeader([]string{"Region", "Used CPUs", "Used MEM", "Committed CPUs", "Committed MEM", "CPUs Coverage", "MEM Coverage"})
	for _, c := range coverages {
		cpuCoverage := 0.0
		if c.CommittedCPUs > 0 {
			cpuCoverage = float64(c.CommittedCPUs) / float64(c.UsedCPUs) * 100
		}
		memCoverage := 0.0
		if c.CommittedMemory > 0 {
			memCoverage = float64(c.CommittedMemory) / float64(c.UsedMemory) * 100
		}
		t.AddRow([]string{
			c.Region,
			fmt.Sprintf("%d", c.UsedCPUs),
			fmt.Sprintf("%.2f GB", float64(c.UsedMemory)/1024),
			fmt.Sprintf("%d", c.CommittedCPUs),
			fmt.Sprintf("%.2f GB", float64(c.CommittedMemory)/1024),
			fmt.Sprintf("%.1f%%", cpuCoverage),
			fmt.Sprintf("%.1f%%", memCoverage),
		})
	}
	return t.Render()
}

func (m *ProxyManager) ClearStorage() {
	// 清理删除时间超过 30 天的代理
	for _, p := range m.DeletedProxies() {
		if p.DeletedAt != nil && time.Since(*p.DeletedAt) > time.Hour*24*30 {
			zlog.Infof("clear deleted proxy: %s", p.Name)
			m.RemoveProxy(p.Name)
		}
	}

	m.Save()
}

func (m *ProxyManager) GetSyncStorageKey(id string) string {
	return fmt.Sprintf("pgate/storage/%s.json", id)
}

func (m *ProxyManager) SyncStorage() {
	if m.Options.SyncStorage == nil || m.Options.SyncStorage.Account == nil {
		return
	}

	filePath := path.Join(m.Options.DataDir, STORAGE_FILE)
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		return
	}

	fileMD5, err := calculateMD5(filePath)
	if err != nil {
		zlog.Errorf("calculate storage file md5 failed: %v", err)
		return
	}

	if m.storageFileMD5 == fileMD5 {
		return
	}

	err = m.Options.SyncStorage.Account.UploadFileToS3(
		m.Options.SyncStorage.Region,
		m.Options.SyncStorage.Bucket,
		m.GetSyncStorageKey(m.Options.ID),
		filePath,
	)
	if err != nil {
		zlog.Errorf("sync storage file to S3 failed: %v", err)
		return
	}

	m.storageFileMD5 = fileMD5
}

func (m *ProxyManager) RestartProxies() {
	for _, p := range m.ActiveDefaultProxies() {
		if p.Status == ProxyStatusOK || p.Status == ProxyStatusFail {
			zlog.Infof("restart trojan proxy: %s", p.Name)
			p.RestartTrojan()
		}
	}
}

func (m *ProxyManager) ActiveGroupProxies(group string) []*Proxy {
	group = strings.TrimPrefix(group, "_")
	proxies := []*Proxy{}
	for _, p := range m.ActiveProxies() {
		if p.Group == group {
			proxies = append(proxies, p)
		}
	}
	return proxies
}

// GetProxiesByName 根据名称获取代理，支持模糊搜索
// 支持的名称格式：
// - 代理名称，如：proxy100,proxy101
// - 代理组名称，如：_group1，支持模糊搜索，会返回该组内所有代理
// - 代理名称和代理组名称混合，如：proxy100,_group1，会返回 proxy100 和 _group1 组内所有代理
func (m *ProxyManager) GetProxiesByName(name string) (proxies []*Proxy) {
	parts := strings.Split(name, ",")
	for _, name := range parts {
		if strings.HasPrefix(name, "_") {
			proxies = append(proxies, m.ActiveGroupProxies(name)...)
		} else {
			proxy := m.GetProxyByName(name)
			if proxy != nil {
				proxies = append(proxies, proxy)
			}
		}
	}
	return
}
